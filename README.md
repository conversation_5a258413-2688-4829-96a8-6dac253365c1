# roon-discord-publish
Uses the Discord Presence API to show what you're listening to on Roon.

Based on 
* an [implementation](https://github.com/jamesxsc/roon-discord-rp) by 615283 (<PERSON>).
* an  [implementation](https://github.com/williamtdr/roon-discord-publish) by williamtdr
* an [implementation](https://github.com/jaredallard/roon-discord-publish) by jaredallard

Changes
- Does not crash on songs with no artist set
- Supports album and artist images
- 
## Requirements

- **Node.js 16.x** (Required for compatibility with Roon API)
- Discord account
- Roon Core running on your network

## Quick Setup

1. **Install Node.js 16.x** (if not already installed):
   - Download from [Node.js website](https://nodejs.org/en/download/releases/) (version 16.x)
   - Or use Node Version Manager:
     ```bash
     # Install nvm first, then:
     nvm install 16.13.2
     nvm use 16.13.2
     ```

2. **Install dependencies**:
   ```bash
   npm run setup
   ```

3. **Configure the application**:
   - Copy `config.example.json` to `config.json`
   - Fill in your Discord, Imgur, and Spotify credentials (see Configuration section below)

4. **Run the application**:
   ```bash
   npm start
   ```

5. **Enable in Roon**:
   - Go to "Extensions" in your Roon client
   - Enable "Discord Rich Presence"

## Alternative Commands

- `npm start` - Start with Node.js version checking (recommended)
- `npm run direct` - Start directly without version checking
- `npm run dev` - Same as direct (for development)
- `npm run check-node` - Check your current Node.js version

## Configuration

You'll need to set up accounts and get API keys for:

1. **Discord Application**:
   - Go to https://discord.com/developers/applications
   - Create a new application
   - Copy the Application ID to `discord.clientId`

2. **Imgur API** (for album art):
   - Go to https://api.imgur.com/oauth2/addclient
   - Create an application
   - Copy the Client ID to `imgur.clientId`

3. **Spotify API** (for music links):
   - Go to https://developer.spotify.com/dashboard
   - Create an app
   - Copy Client ID and Client Secret to `spotify.client` and `spotify.secret`

## Troubleshooting

- **"TypeError: this.ws.on is not a function"**: You're using Node.js 17+ which is incompatible. Use Node.js 16.x
- **"config.json not found"**: Copy `config.example.json` to `config.json` and configure it
- **Extension not appearing in Roon**: Make sure the application is running and check Roon's Extensions settings

Note: You may need to run this in an Administrator Command Prompt or PowerShell on Windows.

## License

GPL-3