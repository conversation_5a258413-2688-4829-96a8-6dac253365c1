# PowerShell script to help set up Node.js 16.x for this project

Write-Host "Checking current Node.js version..." -ForegroundColor Cyan

try {
    $nodeVersion = node --version
    Write-Host "Current Node.js version: $nodeVersion" -ForegroundColor Yellow

    $majorVersion = [int]($nodeVersion -replace 'v(\d+)\..*', '$1')

    if ($majorVersion -eq 16) {
        Write-Host "Node.js 16.x is already installed and active!" -ForegroundColor Green
        Write-Host "You can run: npm start" -ForegroundColor Green
        exit 0
    }

    if ($majorVersion -ge 17) {
        Write-Host "Node.js $majorVersion.x is too new for this project" -ForegroundColor Red
    } else {
        Write-Host "Node.js $majorVersion.x is too old for this project" -ForegroundColor Red
    }
} catch {
    Write-Host "Node.js is not installed or not in PATH" -ForegroundColor Red
}

Write-Host ""
Write-Host "Setting up Node.js 16.x..." -ForegroundColor Cyan
Write-Host ""

# Check if nvm-windows is installed
try {
    $nvmVersion = nvm version
    Write-Host "nvm-windows is installed: $nvmVersion" -ForegroundColor Green

    Write-Host "Installing Node.js 16.13.2..." -ForegroundColor Yellow
    nvm install 16.13.2

    Write-Host "Switching to Node.js 16.13.2..." -ForegroundColor Yellow
    nvm use 16.13.2

    Write-Host "Node.js 16.13.2 is now active!" -ForegroundColor Green
    Write-Host "You can now run: npm start" -ForegroundColor Green

} catch {
    Write-Host "nvm-windows is not installed" -ForegroundColor Red
    Write-Host ""
    Write-Host "Options to install Node.js 16.x:" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Option 1 - Install nvm-windows (Recommended):" -ForegroundColor White
    Write-Host "1. Download from: https://github.com/coreybutler/nvm-windows/releases" -ForegroundColor Gray
    Write-Host "2. Install nvm-setup.zip" -ForegroundColor Gray
    Write-Host "3. Open a new PowerShell window and run this script again" -ForegroundColor Gray
    Write-Host ""
    Write-Host "Option 2 - Direct Node.js installation:" -ForegroundColor White
    Write-Host "1. Download Node.js 16.20.2 from: https://nodejs.org/dist/v16.20.2/" -ForegroundColor Gray
    Write-Host "2. Install the .msi file for Windows" -ForegroundColor Gray
    Write-Host "3. Restart your command prompt" -ForegroundColor Gray
    Write-Host ""
    Write-Host "Option 3 - Try with current Node.js (may not work):" -ForegroundColor White
    Write-Host "Run: npm run direct" -ForegroundColor Gray
}

Write-Host ""
Write-Host "Press any key to continue..."
Read-Host
