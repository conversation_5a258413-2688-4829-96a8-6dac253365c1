{"name": "roon-discord-publish", "version": "0.6.1", "description": "Roon Extension for Discord Rich Presence", "main": "roon-discord-rp.js", "author": "615283 (<PERSON>), synapses, jar<PERSON><PERSON><PERSON>", "license": "Apache-2.0", "dependencies": {"discord-rpc": "^4.0.1", "fs": "^0.0.1-security", "fsa": "^0.5.1", "imgur-anonymous-uploader": "^1.1.6", "imgur-node-api": "^0.1.0", "node-roon-api": "github:roonlabs/node-roon-api", "node-roon-api-image": "github:RoonLabs/node-roon-api-image", "node-roon-api-transport": "github:roonlabs/node-roon-api-transport", "spotify-web-api-node": "^5.0.2"}}