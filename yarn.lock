# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@tokenizer/token@^0.3.0":
  version "0.3.0"
  resolved "https://registry.npmjs.org/@tokenizer/token/-/token-0.3.0.tgz"
  integrity sha512-OvjF+z51L3ov0OyAU0duzsYuvO01PH7x4t6DJx+guahgTnBHkhJdG7soQeTSFLWN3efnHyibZ4Z8l2EuWwJN3A==

async@~0.2.7:
  version "0.2.10"
  resolved "https://registry.npmjs.org/async/-/async-0.2.10.tgz"
  integrity sha512-eAkdoKxU6/LkKDBzLpT+t6Ff5EtfSF4wx1WfJiPEEV7WNLnDaRXk0oVysiEPm262roaachGexwUv94WhSgN5TQ==

asynckit@^0.4.0:
  version "0.4.0"
  resolved "https://registry.npmjs.org/asynckit/-/asynckit-0.4.0.tgz"
  integrity sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q==

aws-sign@~0.2.0:
  version "0.2.0"
  resolved "https://registry.npmjs.org/aws-sign/-/aws-sign-0.2.0.tgz"
  integrity sha512-6P7/Ls5F6++DsKu7iacris7qq/AZSWaX+gT4dtSyUxM82ePxWxaP7Slo82ZO3ZTx6GSKxQHAQlmFvM8e+Dd8ZA==

bindings@^1.3.0:
  version "1.5.0"
  resolved "https://registry.npmjs.org/bindings/-/bindings-1.5.0.tgz"
  integrity sha512-p2q/t/mhvuOj/UeLlV6566GD/guowlr0hHxClI0W9m7MWYkL1F0hLo+0Aexs9HSPCtR1SXQ0TD3MMKrXZajbiQ==
  dependencies:
    file-uri-to-path "1.0.0"

boom@0.3.x:
  version "0.3.1"
  resolved "https://registry.npmjs.org/boom/-/boom-0.3.1.tgz"
  integrity sha512-xWrlXnkK46TjEW7HU5G39AXWuG5aiHz3++zk3bBzF4mfnVCkpcYbwsnLUqMmfZNgPEYS/AI8MH+vmJxH5Kz0PA==
  dependencies:
    hoek "0.4.x"

call-bind@^1.0.7:
  version "1.0.7"
  resolved "https://registry.npmjs.org/call-bind/-/call-bind-1.0.7.tgz"
  integrity sha512-GHTSNSYICQ7scH7sZ+M2rFopRoLh8t2bLSW6BbgrtLsahOIB5iyAVJf9GjWK3cYTDaMj4XdBpM1cA6pIS0Kv2w==
  dependencies:
    es-define-property "^1.0.0"
    es-errors "^1.3.0"
    function-bind "^1.1.2"
    get-intrinsic "^1.2.4"
    set-function-length "^1.2.1"

combined-stream@^1.0.8:
  version "1.0.8"
  resolved "https://registry.npmjs.org/combined-stream/-/combined-stream-1.0.8.tgz"
  integrity sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==
  dependencies:
    delayed-stream "~1.0.0"

combined-stream@~0.0.4:
  version "0.0.7"
  resolved "https://registry.npmjs.org/combined-stream/-/combined-stream-0.0.7.tgz"
  integrity sha512-qfexlmLp9MyrkajQVyjEDb0Vj+KhRgR/rxLiVhaihlT+ZkX0lReqtH6Ack40CvMDERR4b5eFp3CreskpBs1Pig==
  dependencies:
    delayed-stream "0.0.5"

component-emitter@^1.3.0:
  version "1.3.1"
  resolved "https://registry.npmjs.org/component-emitter/-/component-emitter-1.3.1.tgz"
  integrity sha512-T0+barUSQRTUQASh8bx02dl+DhF54GtIDY13Y3m9oWTklKbb3Wv974meRpeZ3lp1JpLVECWWNHC4vaG2XHXouQ==

cookie-jar@~0.2.0:
  version "0.2.0"
  resolved "https://registry.npmjs.org/cookie-jar/-/cookie-jar-0.2.0.tgz"
  integrity sha512-yImk9AY90xjoUsN2fWHoIhVgveXqiZv7LDqUTZEzVBHyzfay8AjcJITUZpz2fTYLh6rnP+7GogiuRCo/5j2epg==

cookiejar@^2.1.2:
  version "2.1.4"
  resolved "https://registry.npmjs.org/cookiejar/-/cookiejar-2.1.4.tgz"
  integrity sha512-LDx6oHrK+PhzLKJU9j5S7/Y3jM/mUHvD/DeI1WQmJn652iPC5Y4TBzC9l+5OMOXlyTTA+SmVUPm0HQUwpD5Jqw==

cryptiles@0.1.x:
  version "0.1.0"
  resolved "https://registry.npmjs.org/cryptiles/-/cryptiles-0.1.0.tgz"
  integrity sha512-WiOGszxSaVHd8T4hlu5Xcqs2uUYxbvotBP171ag2pLPKSwSKeTJYnzd98/YWV3jQYk/rpMHa3r01cQfN8SZrHQ==
  dependencies:
    boom "0.3.x"

debug@^4.1.1:
  version "4.3.7"
  resolved "https://registry.npmjs.org/debug/-/debug-4.3.7.tgz"
  integrity sha512-Er2nc/H7RrMXZBFCEim6TCmMk02Z8vLC2Rbi1KEBggpo0fS6l0S1nnapwmIi3yW/+GOJap1Krg4w0Hg80oCqgQ==
  dependencies:
    ms "^2.1.3"

define-data-property@^1.1.4:
  version "1.1.4"
  resolved "https://registry.npmjs.org/define-data-property/-/define-data-property-1.1.4.tgz"
  integrity sha512-rBMvIzlpA8v6E+SJZoo++HAYqsLrkg7MSfIinMPFhmkorw7X+dOXVJQs+QT69zGkzMyfDnIMN2Wid1+NbL3T+A==
  dependencies:
    es-define-property "^1.0.0"
    es-errors "^1.3.0"
    gopd "^1.0.1"

delayed-stream@~1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/delayed-stream/-/delayed-stream-1.0.0.tgz"
  integrity sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==

delayed-stream@0.0.5:
  version "0.0.5"
  resolved "https://registry.npmjs.org/delayed-stream/-/delayed-stream-0.0.5.tgz"
  integrity sha512-v+7uBd1pqe5YtgPacIIbZ8HuHeLFVNe4mUEyFDXL6KiqzEykjbw+5mXZXpGFgNVasdL4jWKgaKIXrEHiynN1LA==

discord-rpc@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npmjs.org/discord-rpc/-/discord-rpc-4.0.1.tgz"
  integrity sha512-HOvHpbq5STRZJjQIBzwoKnQ0jHplbEWFWlPDwXXKm/bILh4nzjcg7mNqll0UY7RsjFoaXA7e/oYb/4lvpda2zA==
  dependencies:
    node-fetch "^2.6.1"
    ws "^7.3.1"
  optionalDependencies:
    register-scheme "github:devsnek/node-register-scheme"

es-define-property@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/es-define-property/-/es-define-property-1.0.0.tgz"
  integrity sha512-jxayLKShrEqqzJ0eumQbVhTYQM27CfT1T35+gCgDFoL82JLsXqTJ76zv6A0YLOgEnLUMvLzsDsGIrl8NFpT2gQ==
  dependencies:
    get-intrinsic "^1.2.4"

es-errors@^1.3.0:
  version "1.3.0"
  resolved "https://registry.npmjs.org/es-errors/-/es-errors-1.3.0.tgz"
  integrity sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==

fast-safe-stringify@^2.0.7:
  version "2.1.1"
  resolved "https://registry.npmjs.org/fast-safe-stringify/-/fast-safe-stringify-2.1.1.tgz"
  integrity sha512-W+KJc2dmILlPplD/H4K9l9LcAHAfPtP6BY84uVLXQ6Evcz9Lcg33Y2z1IVblT6xdY54PXYVHEv+0Wpq8Io6zkA==

file-type@^16.5.0:
  version "16.5.4"
  resolved "https://registry.npmjs.org/file-type/-/file-type-16.5.4.tgz"
  integrity sha512-/yFHK0aGjFEgDJjEKP0pWCplsPFPhwyfwevf/pVxiN0tmE4L9LmwWxWukdJSHdoCli4VgQLehjJtwQBnqmsKcw==
  dependencies:
    readable-web-to-node-stream "^3.0.0"
    strtok3 "^6.2.4"
    token-types "^4.1.1"

file-uri-to-path@1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/file-uri-to-path/-/file-uri-to-path-1.0.0.tgz"
  integrity sha512-0Zt+s3L7Vf1biwWZ29aARiVYLx7iMGnEUl9x33fbB/j3jR81u/O2LbqK+Bm1CDSNDKVtJ/YjwY7TUd5SkeLQLw==

forever-agent@~0.2.0:
  version "0.2.0"
  resolved "https://registry.npmjs.org/forever-agent/-/forever-agent-0.2.0.tgz"
  integrity sha512-IasWSRIlfPnBZY1K9jEUK3PwsScR4mrcK+aNBJzGoPnW+S9b6f8I8ScyH4cehEOFNqnjGpP2gCaA22gqSV1xQA==

form-data@^3.0.0:
  version "3.0.1"
  resolved "https://registry.npmjs.org/form-data/-/form-data-3.0.1.tgz"
  integrity sha512-RHkBKtLWUVwd7SqRIvCZMEvAMoGUp0XU+seQiZejj0COz3RI3hWP4sCv3gZWWLjJTd7rGwcsF5eKZGii0r/hbg==
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.8"
    mime-types "^2.1.12"

form-data@~0.0.3:
  version "0.0.10"
  resolved "https://registry.npmjs.org/form-data/-/form-data-0.0.10.tgz"
  integrity sha512-Z9/PpT/agxXi80nMpOH6GFD7XOr6mwk5aWMxDt/KMY+Nm7e4FnRMjddM4/mLPJhpmp6alY1F/1JQpRE6z07xng==
  dependencies:
    async "~0.2.7"
    combined-stream "~0.0.4"
    mime "~1.2.2"

formidable@^1.2.2:
  version "1.2.6"
  resolved "https://registry.npmjs.org/formidable/-/formidable-1.2.6.tgz"
  integrity sha512-KcpbcpuLNOwrEjnbpMC0gS+X8ciDoZE1kkqzat4a8vrprf+s9pKNQ/QIwWfbfs4ltgmFl3MD177SNTkve3BwGQ==

fs@^0.0.1-security:
  version "0.0.1-security"
  resolved "https://registry.npmjs.org/fs/-/fs-0.0.1-security.tgz"
  integrity sha512-3XY9e1pP0CVEUCdj5BmfIZxRBTSDycnbqhIOGec9QYtmVH2fbLpj86CFWkrNOkt/Fvty4KZG5lTglL9j/gJ87w==

fsa@^0.5.1:
  version "0.5.1"
  resolved "https://registry.npmjs.org/fsa/-/fsa-0.5.1.tgz"
  integrity sha512-HMdf0zJNXwP5dxROQb/ncUv+9BxuO4NM5R6/pSbUdfu5CUzMOg7+iP4tQUtBIv18PaScukzfxGIqLyJ3d9wg3Q==

function-bind@^1.1.2:
  version "1.1.2"
  resolved "https://registry.npmjs.org/function-bind/-/function-bind-1.1.2.tgz"
  integrity sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==

get-intrinsic@^1.1.3, get-intrinsic@^1.2.4:
  version "1.2.4"
  resolved "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.2.4.tgz"
  integrity sha512-5uYhsJH8VJBTv7oslg4BznJYhDoRI6waYCxMmCdnTrcCrHA/fCFKoTFz2JKKE0HdDFUF7/oQuhzumXJK7paBRQ==
  dependencies:
    es-errors "^1.3.0"
    function-bind "^1.1.2"
    has-proto "^1.0.1"
    has-symbols "^1.0.3"
    hasown "^2.0.0"

gopd@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/gopd/-/gopd-1.0.1.tgz"
  integrity sha512-d65bNlIadxvpb/A2abVdlqKqV563juRnZ1Wtk6s1sIR8uNsXR70xqIzVqxVf1eTqDunwT2MkczEeaezCKTZhwA==
  dependencies:
    get-intrinsic "^1.1.3"

has-property-descriptors@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/has-property-descriptors/-/has-property-descriptors-1.0.2.tgz"
  integrity sha512-55JNKuIW+vq4Ke1BjOTjM2YctQIvCT7GFzHwmfZPGo5wnrgkid0YQtnAleFSqumZm4az3n2BS+erby5ipJdgrg==
  dependencies:
    es-define-property "^1.0.0"

has-proto@^1.0.1:
  version "1.0.3"
  resolved "https://registry.npmjs.org/has-proto/-/has-proto-1.0.3.tgz"
  integrity sha512-SJ1amZAJUiZS+PhsVLf5tGydlaVB8EdFpaSO4gmiUKUOxk8qzn5AIy4ZeJUmh22znIdk/uMAUT2pl3FxzVUH+Q==

has-symbols@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npmjs.org/has-symbols/-/has-symbols-1.0.3.tgz"
  integrity sha512-l3LCuF6MgDNwTDKkdYGEihYjt5pRPbEg46rtlmnSPlUbgmB8LOIrKJbYYFBSbnPaJexMKtiPO8hmeRjRz2Td+A==

hasown@^2.0.0:
  version "2.0.2"
  resolved "https://registry.npmjs.org/hasown/-/hasown-2.0.2.tgz"
  integrity sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==
  dependencies:
    function-bind "^1.1.2"

hawk@~0.10.2:
  version "0.10.2"
  resolved "https://registry.npmjs.org/hawk/-/hawk-0.10.2.tgz"
  integrity sha512-BjpmnZ95odv7KOIsydfNTAxfGOGaVc6xbYL4fozWl45PWjDqskix0LHAekmGkpnrCAI6+AZRvJIXNTAllj+e6w==
  dependencies:
    boom "0.3.x"
    cryptiles "0.1.x"
    hoek "0.7.x"
    sntp "0.1.x"

hoek@0.4.x:
  version "0.4.2"
  resolved "https://registry.npmjs.org/hoek/-/hoek-0.4.2.tgz"
  integrity sha512-Yj/N2TCrS0d8jvZgUpq9sDNt8/ABwTxPJW4+8QT0KXCMxOtRfUCUTEZEYyvMSgfDT3MGvwgO+NHfWPobagAIug==

hoek@0.7.x:
  version "0.7.6"
  resolved "https://registry.npmjs.org/hoek/-/hoek-0.7.6.tgz"
  integrity sha512-z75muWk69yyjWn6nNzJP0pnfgcewtSTs7uBolGUA7kWNdCYZukzHn3sYqUirhXul7qp9WBUwNT/7ieJZNveJqg==

ieee754@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npmjs.org/ieee754/-/ieee754-1.2.1.tgz"
  integrity sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA==

imgur-anonymous-uploader@^1.1.6:
  version "1.1.6"
  resolved "https://registry.npmjs.org/imgur-anonymous-uploader/-/imgur-anonymous-uploader-1.1.6.tgz"
  integrity sha512-/DVy38yu9s2n9q9E3XVGUZGtNFA5jEpM8ryHABnyyJxqi7hasbHoJE9iPlQJR7v9l60ZPPRr1ICqVIKusgSZIQ==
  dependencies:
    file-type "^16.5.0"
    node-fetch "^2.6.7"

imgur-node-api@^0.1.0:
  version "0.1.0"
  resolved "https://registry.npmjs.org/imgur-node-api/-/imgur-node-api-0.1.0.tgz"
  integrity sha512-XW0RGx7SNRLHJTInp53w6aREQSXofZjppvxacguVr8TQKSdcdYDrv6zN9/2XMX+8/3Zju8vHIvj/YrIzLwIiYQ==
  dependencies:
    request "~2.16.6"

inherits@^2.0.3:
  version "2.0.4"
  resolved "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz"
  integrity sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==

ip@^1.1.3:
  version "1.1.9"
  resolved "https://registry.npmjs.org/ip/-/ip-1.1.9.tgz"
  integrity sha512-cyRxvOEpNHNtchU3Ln9KC/auJgup87llfQpQ+t5ghoC/UhL16SWzbueiCsdTnWmqAWl7LadfuwhlqmtOaqMHdQ==

json-stringify-safe@~3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/json-stringify-safe/-/json-stringify-safe-3.0.0.tgz"
  integrity sha512-VSSuxEAawKLYlCabQOR7YDijQ69zPqQBOriUuCgNhlAqtU7RPr41gPpaSs6WkEu+ZOtUequpXWbI51CS+Z/gMQ==

methods@^1.1.2:
  version "1.1.2"
  resolved "https://registry.npmjs.org/methods/-/methods-1.1.2.tgz"
  integrity sha512-iclAHeNqNm68zFtnZ0e+1L2yUIdvzNoauKU4WBA3VvH/vPFieF7qfRlwUZU+DA9P9bPXIS90ulxoUoCH23sV2w==

mime-db@1.52.0:
  version "1.52.0"
  resolved "https://registry.npmjs.org/mime-db/-/mime-db-1.52.0.tgz"
  integrity sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==

mime-types@^2.1.12:
  version "2.1.35"
  resolved "https://registry.npmjs.org/mime-types/-/mime-types-2.1.35.tgz"
  integrity sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==
  dependencies:
    mime-db "1.52.0"

mime@^2.4.6:
  version "2.6.0"
  resolved "https://registry.npmjs.org/mime/-/mime-2.6.0.tgz"
  integrity sha512-USPkMeET31rOMiarsBNIHZKLGgvKc/LrjofAnBlOttf5ajRvqiRA8QsenbcooctK6d6Ts6aqZXBA+XbkKthiQg==

mime@~1.2.2, mime@~1.2.7:
  version "1.2.11"
  resolved "https://registry.npmjs.org/mime/-/mime-1.2.11.tgz"
  integrity sha512-Ysa2F/nqTNGHhhm9MV8ure4+Hc+Y8AWiqUdHxsO7xu8zc92ND9f3kpALHjaP026Ft17UfxrMt95c50PLUeynBw==

ms@^2.1.3:
  version "2.1.3"
  resolved "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz"
  integrity sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==

node-addon-api@^1.3.0:
  version "1.7.2"
  resolved "https://registry.npmjs.org/node-addon-api/-/node-addon-api-1.7.2.tgz"
  integrity sha512-ibPK3iA+vaY1eEjESkQkM0BbCqFOaZMiXRTtdB0u7b4djtY6JnsjvPdUHVMg6xQt3B8fpTTWHI9A+ADjM9frzg==

node-fetch@^2.6.1, node-fetch@^2.6.7:
  version "2.7.0"
  resolved "https://registry.npmjs.org/node-fetch/-/node-fetch-2.7.0.tgz"
  integrity sha512-c4FRfUm/dbcWZ7U+1Wq0AwCyFL+3nt2bEw05wfxSz+DWpWsitgmSgYmy2dQdWyKC1694ELPqMs/YzUSNozLt8A==
  dependencies:
    whatwg-url "^5.0.0"

"node-roon-api-image@github:RoonLabs/node-roon-api-image":
  version "1.0.0"
  resolved "git+ssh://**************/RoonLabs/node-roon-api-image.git#a5f0efaf2dfb5457e91abe326c3a40d865131d32"

"node-roon-api-transport@github:roonlabs/node-roon-api-transport":
  version "2.0.1"
  resolved "git+ssh://**************/roonlabs/node-roon-api-transport.git#2ee60008a4cdb90c34ff3de58bb4b949067f1d20"

"node-roon-api@github:roonlabs/node-roon-api":
  version "1.2.3"
  resolved "git+ssh://**************/roonlabs/node-roon-api.git#7cfaddc63b0d8a8bffe0f71df02d7c73adc728a5"
  dependencies:
    ip "^1.1.3"
    node-uuid "^1.4.7"
    ws ">=3.3.1"

node-uuid@^1.4.7, node-uuid@~1.4.0:
  version "1.4.8"
  resolved "https://registry.npmjs.org/node-uuid/-/node-uuid-1.4.8.tgz"
  integrity sha512-TkCET/3rr9mUuRp+CpO7qfgT++aAxfDRaalQhwPFzI9BY/2rCDn6OfpZOVggi1AXfTPpfkTrg5f5WQx5G1uLxA==

oauth-sign@~0.2.0:
  version "0.2.0"
  resolved "https://registry.npmjs.org/oauth-sign/-/oauth-sign-0.2.0.tgz"
  integrity sha512-4DtiD64CwPJ5vZ636j/KtM7DxWbX1KlkqwbqbEAxI3BCpBrQdrKOv8vC/36U6gfm1CVapy6QmcVxPnXPPQApTA==

object-inspect@^1.13.1:
  version "1.13.2"
  resolved "https://registry.npmjs.org/object-inspect/-/object-inspect-1.13.2.tgz"
  integrity sha512-IRZSRuzJiynemAXPYtPe5BoI/RESNYR7TYm50MC5Mqbd3Jmw5y790sErYw3V6SryFJD64b74qQQs9wn5Bg/k3g==

peek-readable@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npmjs.org/peek-readable/-/peek-readable-4.1.0.tgz"
  integrity sha512-ZI3LnwUv5nOGbQzD9c2iDG6toheuXSZP5esSHBjopsXH4dg19soufvpUGA3uohi5anFtGb2lhAVdHzH6R/Evvg==

qs@^6.9.4:
  version "6.13.0"
  resolved "https://registry.npmjs.org/qs/-/qs-6.13.0.tgz"
  integrity sha512-+38qI9SOr8tfZ4QmJNplMUxqjbe7LKvvZgWdExBOmd+egZTtjLB67Gu0HRX3u/XOq7UU2Nx6nsjvS16Z9uwfpg==
  dependencies:
    side-channel "^1.0.6"

qs@~0.5.4:
  version "0.5.6"
  resolved "https://registry.npmjs.org/qs/-/qs-0.5.6.tgz"
  integrity sha512-KbOrQrP5Ye+0gmq+hwxoJwAFRwExACWqwxj1IDFFgqOw9Poxy3wwSbafd9ZqP6T6ykMfnxM573kt/a4i9ybatQ==

readable-stream@^3.6.0:
  version "3.6.2"
  resolved "https://registry.npmjs.org/readable-stream/-/readable-stream-3.6.2.tgz"
  integrity sha512-9u/sniCrY3D5WdsERHzHE4G2YCXqoG5FTHUiCC4SIbr6XcLZBY05ya9EKjYek9O5xOAwjGq+1JdGBAS7Q9ScoA==
  dependencies:
    inherits "^2.0.3"
    string_decoder "^1.1.1"
    util-deprecate "^1.0.1"

readable-web-to-node-stream@^3.0.0:
  version "3.0.2"
  resolved "https://registry.npmjs.org/readable-web-to-node-stream/-/readable-web-to-node-stream-3.0.2.tgz"
  integrity sha512-ePeK6cc1EcKLEhJFt/AebMCLL+GgSKhuygrZ/GLaKZYEecIgIECf4UaUuaByiGtzckwR4ain9VzUh95T1exYGw==
  dependencies:
    readable-stream "^3.6.0"

"register-scheme@github:devsnek/node-register-scheme":
  version "0.0.2"
  resolved "git+ssh://**************/devsnek/node-register-scheme.git#e7cc9a63a1f512565da44cb57316d9fb10750e17"
  dependencies:
    bindings "^1.3.0"
    node-addon-api "^1.3.0"

request@~2.16.6:
  version "2.16.6"
  resolved "https://registry.npmjs.org/request/-/request-2.16.6.tgz"
  integrity sha512-TfD4kMo40kwuOpO7GYfAZpb2wYdw7yvTIglPNgPPSmp2Fz6MKNvPLla40FQ/ypdhy6B2jRNz3VlCjPD6mnzsmA==
  dependencies:
    aws-sign "~0.2.0"
    cookie-jar "~0.2.0"
    forever-agent "~0.2.0"
    form-data "~0.0.3"
    hawk "~0.10.2"
    json-stringify-safe "~3.0.0"
    mime "~1.2.7"
    node-uuid "~1.4.0"
    oauth-sign "~0.2.0"
    qs "~0.5.4"
    tunnel-agent "~0.2.0"

safe-buffer@~5.2.0:
  version "5.2.1"
  resolved "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz"
  integrity sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==

semver@^7.3.2:
  version "7.6.3"
  resolved "https://registry.npmjs.org/semver/-/semver-7.6.3.tgz"
  integrity sha512-oVekP1cKtI+CTDvHWYFUcMtsK/00wmAEfyqKfNdARm8u1wNVhSgaX7A8d4UuIlUI5e84iEwOhs7ZPYRmzU9U6A==

set-function-length@^1.2.1:
  version "1.2.2"
  resolved "https://registry.npmjs.org/set-function-length/-/set-function-length-1.2.2.tgz"
  integrity sha512-pgRc4hJ4/sNjWCSS9AmnS40x3bNMDTknHgL5UaMBTMyJnU90EgWh1Rz+MC9eFu4BuN/UwZjKQuY/1v3rM7HMfg==
  dependencies:
    define-data-property "^1.1.4"
    es-errors "^1.3.0"
    function-bind "^1.1.2"
    get-intrinsic "^1.2.4"
    gopd "^1.0.1"
    has-property-descriptors "^1.0.2"

side-channel@^1.0.6:
  version "1.0.6"
  resolved "https://registry.npmjs.org/side-channel/-/side-channel-1.0.6.tgz"
  integrity sha512-fDW/EZ6Q9RiO8eFG8Hj+7u/oW+XrPTIChwCOM2+th2A6OblDtYYIpve9m+KvI9Z4C9qSEXlaGR6bTEYHReuglA==
  dependencies:
    call-bind "^1.0.7"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.4"
    object-inspect "^1.13.1"

sntp@0.1.x:
  version "0.1.2"
  resolved "https://registry.npmjs.org/sntp/-/sntp-0.1.2.tgz"
  integrity sha512-6fsOpJYQAQcO/UeW7T9mJwEenJymdU77o+gNiompGAammlSa+C49Oyt79ta/kgVbT13l4JAuKlo8FNvUnVjvEQ==
  dependencies:
    hoek "0.4.x"

spotify-web-api-node@^5.0.2:
  version "5.0.2"
  resolved "https://registry.npmjs.org/spotify-web-api-node/-/spotify-web-api-node-5.0.2.tgz"
  integrity sha512-r82dRWU9PMimHvHEzL0DwEJrzFk+SMCVfq249SLt3I7EFez7R+jeoKQd+M1//QcnjqlXPs2am4DFsGk8/GCsrA==
  dependencies:
    superagent "^6.1.0"

string_decoder@^1.1.1:
  version "1.3.0"
  resolved "https://registry.npmjs.org/string_decoder/-/string_decoder-1.3.0.tgz"
  integrity sha512-hkRX8U1WjJFd8LsDJ2yQ/wWWxaopEsABU1XfkM8A+j0+85JAGppt16cr1Whg6KIbb4okU6Mql6BOj+uup/wKeA==
  dependencies:
    safe-buffer "~5.2.0"

strtok3@^6.2.4:
  version "6.3.0"
  resolved "https://registry.npmjs.org/strtok3/-/strtok3-6.3.0.tgz"
  integrity sha512-fZtbhtvI9I48xDSywd/somNqgUHl2L2cstmXCCif0itOf96jeW18MBSyrLuNicYQVkvpOxkZtkzujiTJ9LW5Jw==
  dependencies:
    "@tokenizer/token" "^0.3.0"
    peek-readable "^4.1.0"

superagent@^6.1.0:
  version "6.1.0"
  resolved "https://registry.npmjs.org/superagent/-/superagent-6.1.0.tgz"
  integrity sha512-OUDHEssirmplo3F+1HWKUrUjvnQuA+nZI6i/JJBdXb5eq9IyEQwPyPpqND+SSsxf6TygpBEkUjISVRN4/VOpeg==
  dependencies:
    component-emitter "^1.3.0"
    cookiejar "^2.1.2"
    debug "^4.1.1"
    fast-safe-stringify "^2.0.7"
    form-data "^3.0.0"
    formidable "^1.2.2"
    methods "^1.1.2"
    mime "^2.4.6"
    qs "^6.9.4"
    readable-stream "^3.6.0"
    semver "^7.3.2"

token-types@^4.1.1:
  version "4.2.1"
  resolved "https://registry.npmjs.org/token-types/-/token-types-4.2.1.tgz"
  integrity sha512-6udB24Q737UD/SDsKAHI9FCRP7Bqc9D/MQUV02ORQg5iskjtLJlZJNdN4kKtcdtwCeWIwIHDGaUsTsCCAa8sFQ==
  dependencies:
    "@tokenizer/token" "^0.3.0"
    ieee754 "^1.2.1"

tr46@~0.0.3:
  version "0.0.3"
  resolved "https://registry.npmjs.org/tr46/-/tr46-0.0.3.tgz"
  integrity sha512-N3WMsuqV66lT30CrXNbEjx4GEwlow3v6rr4mCcv6prnfwhS01rkgyFdjPNBYd9br7LpXV1+Emh01fHnq2Gdgrw==

tunnel-agent@~0.2.0:
  version "0.2.0"
  resolved "https://registry.npmjs.org/tunnel-agent/-/tunnel-agent-0.2.0.tgz"
  integrity sha512-PXy4q1PH88BK0pcGOEMXFAslyBuRWz1wxLfPXTlYFd41eyUgjOALaVGbWJN1ymjbnBzjWunVSKmrrMMh8oLaZA==

util-deprecate@^1.0.1:
  version "1.0.2"
  resolved "https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz"
  integrity sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==

webidl-conversions@^3.0.0:
  version "3.0.1"
  resolved "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-3.0.1.tgz"
  integrity sha512-2JAn3z8AR6rjK8Sm8orRC0h/bcl/DqL7tRPdGZ4I1CjdF+EaMLmYxBHyXuKL849eucPFhvBoxMsflfOb8kxaeQ==

whatwg-url@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/whatwg-url/-/whatwg-url-5.0.0.tgz"
  integrity sha512-saE57nupxk6v3HY35+jzBwYa0rKSy0XR8JSxZPwgLr7ys0IBzhGviA1/TUGJLmSVqs8pb9AnvICXEuOHLprYTw==
  dependencies:
    tr46 "~0.0.3"
    webidl-conversions "^3.0.0"

ws@^7.3.1, ws@>=3.3.1:
  version "7.5.10"
  resolved "https://registry.npmjs.org/ws/-/ws-7.5.10.tgz"
  integrity sha512-+dbF1tHwZpXcbOJdVOkzLDxZP1ailvSxM6ZweXTegylPny803bFhA+vqBYw4s31NSAk4S2Qz+AKXK9a4wkdjcQ==
