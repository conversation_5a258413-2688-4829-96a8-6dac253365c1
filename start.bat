@echo off
echo Starting Roon Discord Rich Presence...
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Node.js is not installed or not in PATH
    echo Please install Node.js 16.x from https://nodejs.org/
    pause
    exit /b 1
)

REM Get Node.js version and check compatibility
for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
echo Current Node.js version: %NODE_VERSION%

REM Extract major version (remove 'v' and get first number)
set MAJOR_VERSION=%NODE_VERSION:~1,2%
if "%MAJOR_VERSION:~1,1%"=="." set MAJOR_VERSION=%NODE_VERSION:~1,1%

if %MAJOR_VERSION% GEQ 17 (
    echo.
    echo ERROR: Node.js %MAJOR_VERSION%.x is too new for this project!
    echo This project requires Node.js 16.x for compatibility with Roon API.
    echo.
    echo Options to fix:
    echo 1. Install nvm-windows: https://github.com/coreybutler/nvm-windows/releases
    echo    Then run: nvm install 16.13.2 ^& nvm use 16.13.2
    echo.
    echo 2. Install Node.js 16.x directly: https://nodejs.org/dist/v16.20.2/
    echo.
    echo 3. Try anyway (may not work): npm run direct
    echo.
    pause
    exit /b 1
)

if %MAJOR_VERSION% LSS 16 (
    echo.
    echo ERROR: Node.js %MAJOR_VERSION%.x is too old for this project!
    echo Please install Node.js 16.x
    pause
    exit /b 1
)

echo Node.js version is compatible!
echo.

REM Check if config.json exists
if not exist "config.json" (
    echo Error: config.json not found!
    echo Please copy config.example.json to config.json and configure it.
    echo See README.md for setup instructions.
    pause
    exit /b 1
)

REM Check if node_modules exists
if not exist "node_modules" (
    echo Installing dependencies...
    npm install
    if %errorlevel% neq 0 (
        echo Error: Failed to install dependencies
        pause
        exit /b 1
    )
)

REM Start the application
npm start

pause
