@echo off
echo Starting Roon Discord Rich Presence...
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Node.js is not installed or not in PATH
    echo Please install Node.js 16.x from https://nodejs.org/
    pause
    exit /b 1
)

REM Check if config.json exists
if not exist "config.json" (
    echo Error: config.json not found!
    echo Please copy config.example.json to config.json and configure it.
    echo See README.md for setup instructions.
    pause
    exit /b 1
)

REM Check if node_modules exists
if not exist "node_modules" (
    echo Installing dependencies...
    npm install
    if %errorlevel% neq 0 (
        echo Error: Failed to install dependencies
        pause
        exit /b 1
    )
)

REM Start the application
npm start

pause
