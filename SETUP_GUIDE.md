# Roon Discord Rich Presence - Setup Guide

## Current Status

✅ **Project is now more self-contained with:**
- Proper package.json scripts
- Node.js version checking
- Helpful error messages and setup instructions
- Multiple startup options
- Automated setup scripts

❌ **Current Issue:**
Your system has Node.js v22.16.0, but this project requires Node.js 16.x due to compatibility issues with the `node-roon-api` dependency.

## Quick Start Options

### Option 1: Use Node Version Manager (Recommended)

1. **Install nvm-windows:**
   - Download from: https://github.com/coreybutler/nvm-windows/releases
   - Download and run `nvm-setup.zip`

2. **Install Node.js 16.x:**
   ```bash
   nvm install 16.13.2
   nvm use 16.13.2
   ```

3. **Run the project:**
   ```bash
   npm start
   ```

### Option 2: Install Node.js 16.x Directly

1. **Download Node.js 16.20.2:**
   - Go to: https://nodejs.org/dist/v16.20.2/
   - Download `node-v16.20.2-x64.msi` for Windows

2. **Install it** (this will replace your current Node.js)

3. **Run the project:**
   ```bash
   npm start
   ```

### Option 3: Use PowerShell Setup Script

Run the automated setup script:
```powershell
npm run setup-node
```

## Available Commands

- `npm start` - Start with version checking (recommended)
- `npm run direct` - Start without version checking (will fail with Node.js 22+)
- `npm run setup-node` - Run PowerShell setup script
- `npm run check-node` - Check current Node.js version
- `npm run setup` - Install dependencies and show config instructions

## Configuration Required

Before running, you need to:

1. **Copy config file:**
   ```bash
   copy config.example.json config.json
   ```

2. **Get API keys and configure:**
   - Discord Application ID
   - Imgur Client ID  
   - Spotify Client ID and Secret

See README.md for detailed configuration instructions.

## Files Added for Self-Containment

- `.nvmrc` - Specifies Node.js version for nvm
- `start.js` - Smart startup script with version checking
- `start.bat` - Windows batch file for easy startup
- `setup-node.ps1` - PowerShell script for Node.js setup
- Updated `package.json` with proper scripts and engine requirements
- Enhanced `README.md` with detailed setup instructions

## Why Node.js 16.x is Required

The `node-roon-api` dependency uses an older WebSocket library that has compatibility issues with Node.js 17+. The error you see:

```
TypeError: this.ws.on is not a function
```

This occurs because the WebSocket API changed in newer Node.js versions.

## Next Steps

1. Choose one of the Node.js installation options above
2. Install Node.js 16.x
3. Run `npm start`
4. Configure your API keys in `config.json`
5. Enable the extension in Roon

The project is now much more self-contained and user-friendly!
