# PowerShell script to extract the downloaded Node.js ZIP file

$nodeDir = "local-node"
$zipFile = "local-node\node-v16.20.2-win-x64.zip"

Write-Host "Extracting Node.js..." -ForegroundColor Cyan

if (Test-Path $zipFile) {
    try {
        # Extract the ZIP file
        Expand-Archive -Path $zipFile -DestinationPath $nodeDir -Force
        Write-Host "Extraction complete!" -ForegroundColor Green
        
        # Test the installation
        $nodePath = "local-node\node-v16.20.2-win-x64\node.exe"
        if (Test-Path $nodePath) {
            $version = & $nodePath --version
            Write-Host "Local Node.js version: $version" -ForegroundColor Green
            Write-Host ""
            Write-Host "Setup complete! You can now run:" -ForegroundColor Green
            Write-Host "  npm run local" -ForegroundColor Yellow
        } else {
            Write-Host "Warning: node.exe not found at expected location" -ForegroundColor Yellow
        }
        
    } catch {
        Write-Host "Error extracting ZIP file: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "Please extract manually:" -ForegroundColor Yellow
        Write-Host "1. Extract $zipFile" -ForegroundColor Gray
        Write-Host "2. Place contents in $nodeDir" -ForegroundColor Gray
    }
} else {
    Write-Host "ZIP file not found: $zipFile" -ForegroundColor Red
    Write-Host "Please run: npm run setup-local-node" -ForegroundColor Yellow
}
